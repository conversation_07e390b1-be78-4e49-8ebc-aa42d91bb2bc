'use client';
import { useState, useEffect } from 'react';
import { CopilotKit } from '@copilotkit/react-core';
import { CopilotPopup } from '@copilotkit/react-ui';
import { useCopilotIntegration } from '@/hooks/useCopilotIntegration';
import { LLMStatusPanel } from '@/components/LLMStatusPanel';
import { ExecutionHistory } from '@/components/ExecutionHistory';
import { DataSourcePanel } from '@/components/DataSourcePanel';
import { DataSource } from '@/types/adapters';
import { TextAdapterImpl } from '@/lib/adapters/text-adapter';
import { CSVAdapterImpl } from '@/lib/adapters/csv-adapter';
import { useLLMStore } from '@/lib/llm-state';

function MainApp() {
  const [dataSources, setDataSources] = useState<DataSource[]>([]);
  const [apiKey, setApiKey] = useState('');
  const [model, setModel] = useState('anthropic/claude-4-sonnet-20250522');
  const { setConfig } = useLLMStore();

  const { executionHistory } = useCopilotIntegration(dataSources);

  useEffect(() => {
    if (apiKey) {
      setConfig({ apiKey, model });
    }
  }, [apiKey, model, setConfig]);

  useEffect(() => {
    const sampleData = [
      { name: 'Alice', age: 30, city: 'New York', salary: 75000 },
      { name: 'Bob', age: 25, city: 'San Francisco', salary: 80000 },
      { name: 'Charlie', age: 35, city: 'Chicago', salary: 70000 },
      { name: 'Diana', age: 28, city: 'Boston', salary: 72000 },
      { name: 'Eve', age: 32, city: 'Seattle', salary: 78000 },
    ];

    const sampleText = `
      PowerChat is an innovative AI-powered code execution platform that enables users to write and execute JavaScript code with access to typed data sources and recursive AI calls. 
      
      The platform provides a comprehensive suite of data adapters including CSV, JSON, TimeSeries, and Text adapters, each with specialized operations for data manipulation and analysis.
      
      Key features include:
      - Real-time LLM request tracking and rate limit management
      - Intelligent code execution with proper error handling
      - Interactive chat interface powered by CopilotKit
      - Support for multiple data source types with typed interfaces
      - OpenRouter integration for flexible model switching
    `;

    const initialSources: DataSource[] = [
      new CSVAdapterImpl(sampleData, 'Employee Data', 'Sample employee information with names, ages, cities, and salaries'),
      new TextAdapterImpl(sampleText, 'Platform Documentation', 'Documentation text about PowerChat platform features'),
    ];

    setDataSources(initialSources);
  }, []);

  const addSampleDataSource = () => {
    const newJsonData = {
      products: [
        { id: 1, name: 'Laptop', price: 999, category: 'Electronics' },
        { id: 2, name: 'Book', price: 15, category: 'Education' },
        { id: 3, name: 'Coffee', price: 5, category: 'Food' },
      ],
      metadata: {
        total: 3,
        lastUpdated: new Date().toISOString(),
      }
    };

    const jsonAdapter = {
      type: 'json' as const,
      name: 'Product Catalog',
      description: 'Sample product catalog with prices and categories',
      schema: { products: 'array', metadata: 'object' },
      operations: ['query', 'filter', 'transform'],
      structure: 'nested' as const,
      keys: ['products', 'metadata'],
      sampleData: newJsonData,
      query: (path: string) => {
        const keys = path.split('.');
        let result: any = newJsonData;
        for (const key of keys) {
          result = result?.[key];
        }
        return result;
      },
      filter: (predicate: (item: any) => boolean) => {
        return newJsonData.products.filter(predicate);
      },
      transform: (transformer: (item: any) => any) => {
        return newJsonData.products.map(transformer);
      },
    };

    setDataSources(prev => [...prev, jsonAdapter]);
  };

  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <div className="max-w-7xl mx-auto">
        <header className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-4">PowerChat - AI Code Execution</h1>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                OpenRouter API Key
              </label>
              <input
                type="password"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="Enter your OpenRouter API key"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Model
              </label>
              <select
                value={model}
                onChange={(e) => setModel(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="anthropic/claude-4-sonnet-20250522">Claude 4 Sonnet</option>
                <option value="deepseek/deepseek-r1-0528:free">DeepSeek R1</option>
                <option value="google/gemini-2.5-pro-preview">Gemini 2.5 Pro</option>
                <option value="google/gemini-2.5-flash-preview">Gemini 2.5 Flash</option>
                <option value="google/gemini-2.0-flash-001">Gemini 2.0 Flash</option>
              </select>
            </div>
          </div>

          {!apiKey && (
            <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-yellow-800 text-sm">
                Please enter your OpenRouter API key to enable AI code execution and LLM calls.
                Get your key at <a href="https://openrouter.ai" target="_blank" rel="noopener noreferrer" className="underline">openrouter.ai</a>
              </p>
            </div>
          )}
        </header>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <DataSourcePanel
            dataSources={dataSources}
            onAddDataSource={addSampleDataSource}
          />
          <LLMStatusPanel />
        </div>

        <ExecutionHistory history={executionHistory} />
      </div>

      <CopilotPopup
        instructions="You are PowerChat, an AI assistant that helps users write and execute JavaScript code with access to typed data sources and recursive LLM calls. Use the write_and_execute_code action to process data intelligently while being mindful of rate limits."
        labels={{
          title: "PowerChat AI Assistant",
          initial: "Hi! I can help you write and execute code with your data sources. What would you like to analyze?",
        }}
      />
    </div>
  );
}

export default function Home() {
  return (
    <CopilotKit runtimeUrl="/api/copilotkit">
      <MainApp />
    </CopilotKit>
  );
}