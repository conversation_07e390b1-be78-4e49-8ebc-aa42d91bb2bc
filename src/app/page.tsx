'use client';
import { useState, useEffect } from 'react';
import { useChat } from 'ai/react';
import { LLMStatusPanel } from '@/components/LLMStatusPanel';

import { DataSourcePanel } from '@/components/DataSourcePanel';
import { DataSource } from '@/types/adapters';
import { TextAdapterImpl } from '@/lib/adapters/text-adapter';
import { CSVAdapterImpl } from '@/lib/adapters/csv-adapter';
import { useLLMStore } from '@/lib/llm-state';

interface MainAppProps {
  apiKey: string;
  setApiKey: (key: string) => void;
  model: string;
  setModel: (model: string) => void;
}

function MainApp({ apiKey, setApiKey, model, setModel }: MainAppProps) {
  const [dataSources, setDataSources] = useState<DataSource[]>([]);
  const { setConfig } = useLLMStore();

  const { messages, input, handleInputChange, handleSubmit, isLoading } = useChat({
    api: '/api/chat',
    body: {
      apiKey,
      model,
      dataSources,
    },
  });

  useEffect(() => {
    if (apiKey) {
      setConfig({ apiKey, model });
    }
  }, [apiKey, model, setConfig]);

  useEffect(() => {
    const sampleData = [
      { name: 'Alice', age: 30, city: 'New York', salary: 75000 },
      { name: 'Bob', age: 25, city: 'San Francisco', salary: 80000 },
      { name: 'Charlie', age: 35, city: 'Chicago', salary: 70000 },
      { name: 'Diana', age: 28, city: 'Boston', salary: 72000 },
      { name: 'Eve', age: 32, city: 'Seattle', salary: 78000 },
    ];

    const sampleText = `
      PowerChat is an innovative AI-powered code execution platform that enables users to write and execute JavaScript code with access to typed data sources and recursive AI calls. 
      
      The platform provides a comprehensive suite of data adapters including CSV, JSON, TimeSeries, and Text adapters, each with specialized operations for data manipulation and analysis.
      
      Key features include:
      - Real-time LLM request tracking and rate limit management
      - Intelligent code execution with proper error handling
      - Interactive chat interface powered by CopilotKit
      - Support for multiple data source types with typed interfaces
      - OpenRouter integration for flexible model switching
    `;

    const initialSources: DataSource[] = [
      new CSVAdapterImpl(sampleData, 'Employee Data', 'Sample employee information with names, ages, cities, and salaries'),
      new TextAdapterImpl(sampleText, 'Platform Documentation', 'Documentation text about PowerChat platform features'),
    ];

    setDataSources(initialSources);
  }, []);

  const addSampleDataSource = () => {
    const newJsonData = {
      products: [
        { id: 1, name: 'Laptop', price: 999, category: 'Electronics' },
        { id: 2, name: 'Book', price: 15, category: 'Education' },
        { id: 3, name: 'Coffee', price: 5, category: 'Food' },
      ],
      metadata: {
        total: 3,
        lastUpdated: new Date().toISOString(),
      }
    };

    const jsonAdapter = {
      type: 'json' as const,
      name: 'Product Catalog',
      description: 'Sample product catalog with prices and categories',
      schema: { products: 'array', metadata: 'object' },
      operations: ['query', 'filter', 'transform'],
      structure: 'nested' as const,
      keys: ['products', 'metadata'],
      sampleData: newJsonData,
      query: (path: string) => {
        const keys = path.split('.');
        let result: any = newJsonData;
        for (const key of keys) {
          result = result?.[key];
        }
        return result;
      },
      filter: (predicate: (item: any) => boolean) => {
        return newJsonData.products.filter(predicate);
      },
      transform: (transformer: (item: any) => any) => {
        return newJsonData.products.map(transformer);
      },
    };

    setDataSources(prev => [...prev, jsonAdapter]);
  };

  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <div className="max-w-7xl mx-auto">
        <header className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-4">PowerChat - AI Code Execution</h1>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                OpenRouter API Key
              </label>
              <input
                type="password"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="Enter your OpenRouter API key"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Model
              </label>
              <select
                value={model}
                onChange={(e) => setModel(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="anthropic/claude-4-sonnet-20250522">Claude 4 Sonnet</option>
                <option value="deepseek/deepseek-r1-0528:free">DeepSeek R1</option>
                <option value="google/gemini-2.5-pro-preview">Gemini 2.5 Pro</option>
                <option value="google/gemini-2.5-flash-preview">Gemini 2.5 Flash</option>
                <option value="google/gemini-2.0-flash-001">Gemini 2.0 Flash</option>
              </select>
            </div>
          </div>

          {!apiKey && (
            <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-yellow-800 text-sm">
                Please enter your OpenRouter API key to enable AI code execution and LLM calls.
                Get your key at <a href="https://openrouter.ai" target="_blank" rel="noopener noreferrer" className="underline">openrouter.ai</a>
              </p>
            </div>
          )}
        </header>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          <DataSourcePanel
            dataSources={dataSources}
            onAddDataSource={addSampleDataSource}
          />
          <LLMStatusPanel />
        </div>

        {/* Chat Interface */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-bold text-gray-800 mb-4">AI Assistant</h2>

          {/* Messages */}
          <div className="h-96 overflow-y-auto mb-4 p-4 border border-gray-200 rounded-md">
            {messages.length === 0 ? (
              <div className="text-gray-500 text-center">
                Hi! I can help you write and execute code with your data sources. What would you like to analyze?
              </div>
            ) : (
              messages.map((message) => (
                <div
                  key={message.id}
                  className={`mb-4 ${message.role === 'user' ? 'text-right' : 'text-left'
                    }`}
                >
                  <div
                    className={`inline-block p-3 rounded-lg max-w-xs lg:max-w-md ${message.role === 'user'
                      ? 'bg-blue-500 text-white'
                      : 'bg-gray-100 text-gray-800'
                      }`}
                  >
                    {message.content}
                  </div>

                  {/* Render tool calls */}
                  {/* @ts-ignore - toolInvocations is deprecated but still functional */}
                  {message.toolInvocations && message.toolInvocations.length > 0 && (
                    <div className="mt-2 space-y-2 max-w-2xl">
                      {/* @ts-ignore */}
                      {message.toolInvocations.map((toolInvocation: any) => (
                        <div key={toolInvocation.toolCallId} className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                          <div className="flex items-center mb-2">
                            <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                            <span className="text-sm font-medium text-blue-800">
                              Code Execution
                            </span>
                          </div>

                          {toolInvocation.state === 'call' && (
                            <div>
                              {toolInvocation.args.explanation && (
                                <div className="mb-2">
                                  <span className="text-xs text-blue-600 font-medium">Explanation:</span>
                                  <p className="text-sm text-blue-700">{toolInvocation.args.explanation}</p>
                                </div>
                              )}
                              <div className="mb-2">
                                <span className="text-xs text-blue-600 font-medium">Code:</span>
                                <pre className="text-xs bg-gray-800 text-green-400 p-2 rounded mt-1 overflow-x-auto">
                                  <code>{toolInvocation.args.code}</code>
                                </pre>
                              </div>
                              <div className="text-xs text-blue-500">Executing...</div>
                            </div>
                          )}

                          {toolInvocation.state === 'result' && (
                            <div>
                              {toolInvocation.args.explanation && (
                                <div className="mb-2">
                                  <span className="text-xs text-blue-600 font-medium">Explanation:</span>
                                  <p className="text-sm text-blue-700">{toolInvocation.args.explanation}</p>
                                </div>
                              )}
                              <div className="mb-2">
                                <span className="text-xs text-blue-600 font-medium">Code:</span>
                                <pre className="text-xs bg-gray-800 text-green-400 p-2 rounded mt-1 overflow-x-auto">
                                  <code>{toolInvocation.args.code}</code>
                                </pre>
                              </div>

                              <div className="mt-2">
                                <span className="text-xs text-blue-600 font-medium">Result:</span>
                                <div className={`text-sm p-2 rounded mt-1 ${toolInvocation.result.success
                                  ? 'bg-green-50 text-green-800 border border-green-200'
                                  : 'bg-red-50 text-red-800 border border-red-200'
                                  }`}>
                                  {toolInvocation.result.success ? '✅' : '❌'}
                                  {toolInvocation.result.result !== undefined && (
                                    <pre className="mt-1 text-xs overflow-x-auto">
                                      {JSON.stringify(toolInvocation.result.result, null, 2)}
                                    </pre>
                                  )}
                                  {toolInvocation.result.error && (
                                    <div className="mt-1 text-xs">
                                      <strong>Error:</strong> {toolInvocation.result.error}
                                    </div>
                                  )}
                                  {toolInvocation.result.logs && toolInvocation.result.logs.length > 0 && (
                                    <div className="mt-1">
                                      <strong className="text-xs">Logs:</strong>
                                      <pre className="text-xs mt-1 bg-gray-100 p-1 rounded">
                                        {toolInvocation.result.logs.join('\n')}
                                      </pre>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ))
            )}
            {isLoading && (
              <div className="text-left mb-4">
                <div className="inline-block p-3 rounded-lg bg-gray-100 text-gray-800">
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600 mr-2"></div>
                    Thinking...
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Input */}
          <form onSubmit={handleSubmit} className="flex gap-2">
            <input
              value={input}
              onChange={handleInputChange}
              placeholder="Ask me to analyze your data or write code..."
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              disabled={!apiKey || isLoading}
            />
            <button
              type="submit"
              disabled={!apiKey || isLoading}
              className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              Send
            </button>
          </form>

          {!apiKey && (
            <div className="mt-2 text-sm text-yellow-600">
              Please enter your OpenRouter API key above to start chatting.
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default function Home() {
  const [apiKey, setApiKey] = useState('');
  const [model, setModel] = useState('anthropic/claude-4-sonnet-20250522');

  return (
    <MainApp
      apiKey={apiKey}
      setApiKey={setApiKey}
      model={model}
      setModel={setModel}
    />
  );
}