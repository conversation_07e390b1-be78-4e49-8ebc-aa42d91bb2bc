import { createOpenA<PERSON> } from "@ai-sdk/openai";
import { streamText } from "ai";
import { NextRequest } from "next/server";

export async function POST(req: NextRequest) {
  try {
    const { messages, apiKey, model } = await req.json();

    console.log("Chat API Route:", {
      apiKey: apiKey ? `${apiKey.substring(0, 10)}...` : "none",
      model: model || "anthropic/claude-3-sonnet",
      messageCount: messages?.length || 0,
    });

    if (!apiKey) {
      return new Response("API key is required", { status: 400 });
    }

    // Configure OpenAI provider for OpenRouter
    const openai = createOpenAI({
      apiKey: apiKey,
      baseURL: "https://openrouter.ai/api/v1",
      headers: {
        "HTTP-Referer":
          process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000",
        "X-Title": "PowerChat - AI Code Execution",
      },
    });

    const result = await streamText({
      model: openai(model || "anthropic/claude-3-sonnet"),
      messages,
      system: `You are PowerChat, an AI assistant that helps users write and execute JavaScript code with access to typed data sources and recursive LLM calls. 

You have access to a write_and_execute_code function that can:
- Execute JavaScript code with access to data_source array
- Use an llm() function for recursive AI calls
- Access metadata about available data sources
- Log output with console functions

Be helpful, concise, and focus on practical code solutions.`,
    });

    return result.toDataStreamResponse();
  } catch (error) {
    console.error("Chat API error:", error);
    return new Response("Internal server error", { status: 500 });
  }
}
