import { createOpenA<PERSON> } from "@ai-sdk/openai";
import { streamText } from "ai";
import { NextRequest } from "next/server";
import { z } from "zod";
import { CodeExecutor } from "@/lib/code-execution";

export async function POST(req: NextRequest) {
  try {
    const { messages, apiKey, model, dataSources = [] } = await req.json();

    console.log("Chat API Route:", {
      apiKey: apiKey ? `${apiKey.substring(0, 10)}...` : "none",
      model: model || "anthropic/claude-3-sonnet",
      messageCount: messages?.length || 0,
      dataSourceCount: dataSources?.length || 0,
    });

    if (!apiKey) {
      return new Response("API key is required", { status: 400 });
    }

    // Initialize code executor with data sources
    const codeExecutor = new CodeExecutor(dataSources);

    // Configure OpenAI provider for OpenRouter
    const openai = createOpenAI({
      apiKey: apiKey,
      baseURL: "https://openrouter.ai/api/v1",
      headers: {
        "HTTP-Referer":
          process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000",
        "X-Title": "PowerChat - AI Code Execution",
      },
    });

    const result = await streamText({
      model: openai(model || "anthropic/claude-3-sonnet"),
      messages,
      system: `You are PowerChat, an AI assistant that helps users write and execute JavaScript code with access to typed data sources and recursive LLM calls.

You have access to a write_and_execute_code tool that can:
- Execute JavaScript code with access to data_source array
- Use an llm() function for recursive AI calls
- Access metadata about available data sources
- Log output with console functions

${codeExecutor.getContextInfo()}

Be helpful, concise, and focus on practical code solutions. When users ask for data analysis or code execution, use the write_and_execute_code tool.`,
      tools: {
        write_and_execute_code: {
          description: `Write and execute JavaScript code with access to typed data sources and an llm() function.

Available in the execution context:
- data_source[i] - Adapter objects with methods like .sampleData, .filter(), .query()
- getData(i) - Helper function to get raw data array from adapter i
- getAllData() - Helper function to get all raw data arrays
- metadata - Information about available data sources
- llm(prompt) - Function to spawn AI instances (be mindful of rate limits)
- console.log/error/warn/info - Logging functions

IMPORTANT: To access actual data arrays, use:
- For CSV/JSON data: data_source[i].sampleData or getData(i)
- For text data: data_source[i].sampleText or getData(i)
- Do NOT call .slice() directly on data_source[i] - it's an adapter object, not an array

${codeExecutor.getContextInfo()}

Consider rate limits when calling llm(). Process data in appropriate chunks and balance AI calls with local processing.`,
          parameters: z.object({
            code: z
              .string()
              .describe(
                "JavaScript code to execute. The code has access to data_source array, metadata object, and llm() function."
              ),
            explanation: z
              .string()
              .optional()
              .describe(
                "Brief explanation of what the code does and why this approach was chosen."
              ),
          }),
          execute: async ({ code, explanation }) => {
            try {
              console.log("Executing code:", {
                code: code.substring(0, 100) + "...",
                explanation,
              });

              const result = await codeExecutor.executeCode(code);

              let response = `Code executed successfully!`;
              if (explanation) {
                response += `\n\nExplanation: ${explanation}`;
              }

              if (result.error) {
                response += `\n\n❌ Error: ${result.error}`;
              } else if (result.result !== undefined) {
                response += `\n\n✅ Result: ${JSON.stringify(
                  result.result,
                  null,
                  2
                )}`;
              }

              if (result.logs.length > 0) {
                response += `\n\nLogs:\n${result.logs.join("\n")}`;
              }

              return {
                success: !result.error,
                result: result.result,
                error: result.error,
                logs: result.logs,
                executedCode: code,
                explanation,
                response,
              };
            } catch (error) {
              const errorMessage =
                error instanceof Error ? error.message : "Unknown error";

              // Log error to backend console for debugging purposes
              console.error("Tool execution error:", {
                error: errorMessage,
                code: code.substring(0, 200) + "...",
                explanation,
                stack: error instanceof Error ? error.stack : undefined,
              });

              return {
                success: false,
                error: errorMessage,
                response: `❌ Execution failed: ${errorMessage}`,
              };
            }
          },
        },
      },
    });

    return result.toDataStreamResponse();
  } catch (error) {
    console.error("Chat API error:", error);
    return new Response("Internal server error", { status: 500 });
  }
}
