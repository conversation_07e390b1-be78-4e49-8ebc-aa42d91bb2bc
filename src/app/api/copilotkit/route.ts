import { CopilotRuntime, OpenAIAdapter } from "@copilotkit/backend";
import { NextRequest } from "next/server";
import OpenAI from "openai";

export async function POST(req: NextRequest) {
  const runtime = new CopilotRuntime();

  // Get API key and model from request headers
  let apiKey =
    req.headers.get("x-openrouter-api-key") || process.env.OPENROUTER_API_KEY;
  let model =
    req.headers.get("x-openrouter-model") || "anthropic/claude-3-sonnet";

  console.log("CopilotKit API Route - Headers:", {
    apiKey: apiKey ? `${apiKey.substring(0, 10)}...` : "none",
    model: model,
    allHeaders: Object.fromEntries(req.headers.entries()),
  });

  // Fallback to dummy key if still no API key
  if (!apiKey) {
    console.log("No API key found, using dummy key");
    apiKey = "dummy-key";
  }

  console.log("Using OpenRouter with:", {
    baseURL: "https://openrouter.ai/api/v1",
    model: model,
    hasApiKey: !!apiKey && apiKey !== "dummy-key",
  });

  // Create a custom OpenAI instance configured for OpenRouter
  const openai = new OpenAI({
    apiKey: apiKey,
    baseURL: "https://openrouter.ai/api/v1",
    defaultHeaders: {
      "HTTP-Referer":
        process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000",
      "X-Title": "PowerChat - AI Code Execution",
    },
  });

  console.log("OpenAI instance created:", {
    baseURL: openai.baseURL,
    hasCompletions: !!openai.chat?.completions,
    openaiKeys: Object.keys(openai),
  });

  try {
    const adapter = new OpenAIAdapter({
      openai: openai,
      model: model,
    });

    console.log("OpenAIAdapter created successfully");

    return runtime.response(req, adapter);
  } catch (error) {
    console.error("Error creating OpenAIAdapter:", error);
    throw error;
  }
}
