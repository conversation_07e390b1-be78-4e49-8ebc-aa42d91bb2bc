import { DataSource, DataSourceMetadata } from "@/types/adapters";
import { llmFunction } from "./llm-state";

export interface CodeExecutionContext {
  data_source: DataSource[];
  metadata: DataSourceMetadata;
  llm: (prompt: string) => Promise<string>;
  // Helper function to get raw data from adapters
  getData: (index: number) => any;
  // Helper function to get all raw data as arrays
  getAllData: () => any[];
}

export class CodeExecutor {
  private dataSources: DataSource[] = [];
  private context: CodeExecutionContext;

  constructor(dataSources: DataSource[] = []) {
    this.dataSources = dataSources;
    this.context = this.createContext();
  }

  private createContext(): CodeExecutionContext {
    return {
      data_source: this.dataSources,
      metadata: {
        count: this.dataSources.length,
        sources: this.dataSources.map((source, index) => ({
          index,
          type: source.type,
          name: source.name,
          description: source.description,
        })),
      },
      llm: llmFunction,
      getData: (index: number) => {
        const source = this.dataSources[index];
        if (!source) return null;

        // Return the actual data based on adapter type
        if (source.type === "csv" || source.type === "json") {
          return source.sampleData;
        } else if (source.type === "text") {
          return source.sampleText;
        }
        return source;
      },
      getAllData: () => {
        return this.dataSources.map((source, index) => {
          if (source.type === "csv" || source.type === "json") {
            return source.sampleData;
          } else if (source.type === "text") {
            return source.sampleText;
          }
          return source;
        });
      },
    };
  }

  setDataSources(dataSources: DataSource[]) {
    this.dataSources = dataSources;
    this.context = this.createContext();
  }

  async executeCode(
    code: string
  ): Promise<{ result: any; error?: string; logs: string[] }> {
    const logs: string[] = [];

    const originalConsole = {
      log: console.log,
      error: console.error,
      warn: console.warn,
      info: console.info,
    };

    const mockConsole = {
      log: (...args: any[]) => logs.push(`LOG: ${args.join(" ")}`),
      error: (...args: any[]) => logs.push(`ERROR: ${args.join(" ")}`),
      warn: (...args: any[]) => logs.push(`WARN: ${args.join(" ")}`),
      info: (...args: any[]) => logs.push(`INFO: ${args.join(" ")}`),
    };

    try {
      const asyncFunction = new Function(
        "data_source",
        "metadata",
        "llm",
        "console",
        "getData",
        "getAllData",
        `
        return (async () => {
          ${code}
        })();
        `
      );

      const result = await asyncFunction(
        this.context.data_source,
        this.context.metadata,
        this.context.llm,
        mockConsole,
        this.context.getData,
        this.context.getAllData
      );

      return { result, logs };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error";

      // Log error to backend console for debugging purposes
      console.error("Code execution error:", {
        error: errorMessage,
        code: code.substring(0, 200) + "...",
        stack: error instanceof Error ? error.stack : undefined,
      });

      return {
        result: null,
        error: errorMessage,
        logs: [...logs, `EXECUTION ERROR: ${errorMessage}`],
      };
    } finally {
      Object.assign(console, originalConsole);
    }
  }

  getContextInfo(): string {
    const sourceDescriptions = this.context.metadata.sources
      .map(
        (source) =>
          `data_source[${source.index}] (${source.type}: ${source.name} - ${source.description})`
      )
      .join(", ");

    return `You have access to ${this.context.metadata.count} data sources: ${sourceDescriptions}.

Data Access Patterns:
- data_source[i] = adapter object with methods like .sampleData, .filter(), .query()
- getData(i) = helper function to get raw data array from adapter i
- getAllData() = helper function to get all raw data arrays
- For CSV/JSON: use data_source[i].sampleData or getData(i) to get the actual array
- For text: use data_source[i].sampleText or getData(i) to get the text content

Use llm() for AI calls but be aware of rate limits.`;
  }
}
